package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.dto.LoginDto;
import com.xtgl.ssystem.common.dto.LoginResponseDto;
import com.xtgl.ssystem.common.entity.Result;
import com.xtgl.ssystem.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 登录控制器
 */
@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor
public class LoginController {
    
    private final UserService userService;
    
    /**
     * 用户登录
     */
    @WebLog(title = "用户登录")
    @PostMapping("/login")
    public Result<LoginResponseDto> login(@Validated @RequestBody LoginDto loginDto) {
        try {
            LoginResponseDto response = userService.login(loginDto);
            return Result.success(response);
        } catch (Exception e) {
            log.error("登录失败：{}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }
} 