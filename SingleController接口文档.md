# SingleController 航天器信息接口文档

## 概述

SingleController 是单机配置控制器，提供航天器信息搜索和完整的层级管理功能，包括分系统、单机、模块的增删改查，以及遥测代号绑定相关操作。

## 基础路径

所有接口的基础路径为: `/single`

## 通用响应格式

所有接口均使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": "响应数据内容"
}
```

- `code`: 响应码，200表示成功，其他表示失败
- `message`: 响应消息
- `data`: 响应数据，具体结构根据接口而定

---

## 1. 航天器信息接口

### 1.1 航天器信息搜索

**功能描述**: 在航天器信息展示基础上增加多条件过滤能力，支持型号、名称、负责人、研制单位四个维度的模糊/精确查询

**端点路径**: `GET /single/craftsearch`

**HTTP方法**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| model | String | 否 | - | 型号，精确匹配 |
| name | String | 否 | - | 名称，模糊匹配 |
| header | String | 否 | - | 负责人，模糊匹配 |
| company | String | 否 | - | 研制单位，模糊匹配 |
| pageNo | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 10 | 每页条数 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "model": "SY-01",
        "name": "神舟一号",
        "header": "张三",
        "company": "航天科技集团"
      }
    ],
    "page": 1,
    "size": 10
  }
}
```

---

## 2. 分系统管理接口

### 2.1 添加分系统

**功能描述**: 给指定卫星添加分系统

**端点路径**: `POST /single/subsystemadd`

**HTTP方法**: POST

**请求体**:
```json
{
  "name": "分系统名称",
  "spacecraftId": 1
}
```

**请求体字段说明**:
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 分系统名称 |
| spacecraftId | Long | 是 | 所属航天器ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": 123
}
```

### 2.2 查询分系统列表

**功能描述**: 查询某卫星下的所有分系统（第1级）

**端点路径**: `GET /single/satellite/{satelliteId}/subsystems`

**HTTP方法**: GET

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| satelliteId | Long | 是 | 航天器ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "动力分系统",
      "spacecraftId": 1
    },
    {
      "id": 2,
      "name": "控制分系统",
      "spacecraftId": 1
    }
  ]
}
```

### 2.3 删除分系统

**功能描述**: 删除指定分系统（级联删除其下所有单机及模块）

**端点路径**: `DELETE /single/subsystems/{subsystemId}`

**HTTP方法**: DELETE

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| subsystemId | Long | 是 | 分系统ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2.4 编辑分系统

**功能描述**: 编辑指定分系统信息

**端点路径**: `PUT /single/subsystems/{subsystemId}`

**HTTP方法**: PUT

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| subsystemId | Long | 是 | 分系统ID |

**请求体**:
```json
{
  "name": "新的分系统名称",
  "spacecraftId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 3. 单机管理接口

### 3.1 添加单机

**功能描述**: 给指定分系统添加单机

**端点路径**: `POST /single/singleadd`

**HTTP方法**: POST

**请求体**:
```json
{
  "name": "单机名称",
  "subsystemId": 1,
  "spacecraftId": 1
}
```

**请求体字段说明**:
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 单机名称 |
| subsystemId | Long | 是 | 所属分系统ID |
| spacecraftId | Long | 是 | 所属航天器ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": 456
}
```

### 3.2 查询单机列表

**功能描述**: 查询某分系统下的所有单机（第2级）

**端点路径**: `GET /single/subsystem/{subsystemId}/singles`

**HTTP方法**: GET

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| subsystemId | Long | 是 | 分系统ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "推力器控制单机",
      "subsystemId": 1,
      "spacecraftId": 1
    }
  ]
}
```

### 3.3 删除单机

**功能描述**: 删除指定单机（级联删除其下所有模块）

**端点路径**: `DELETE /single/singles/{singleId}`

**HTTP方法**: DELETE

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| singleId | Long | 是 | 单机ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.4 编辑单机

**功能描述**: 编辑指定单机信息

**端点路径**: `PUT /single/singles/{singleId}`

**HTTP方法**: PUT

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| singleId | Long | 是 | 单机ID |

**请求体**:
```json
{
  "name": "新的单机名称",
  "subsystemId": 1,
  "spacecraftId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 4. 模块管理接口

### 4.1 添加模块

**功能描述**: 给指定单机添加模块

**端点路径**: `POST /single/moduleadd`

**HTTP方法**: POST

**请求体**:
```json
{
  "name": "模块名称",
  "subsystemId": 1,
  "spacecraftId": 1,
  "singleId": 1
}
```

**请求体字段说明**:
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 模块名称 |
| subsystemId | Long | 是 | 所属分系统ID |
| spacecraftId | Long | 是 | 所属航天器ID |
| singleId | Long | 是 | 所属单机ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": 789
}
```

### 4.2 查询模块列表

**功能描述**: 查询某单机下的所有模块（第3级）

**端点路径**: `GET /single/single/{singleId}/modules`

**HTTP方法**: GET

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| singleId | Long | 是 | 单机ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "温度传感器模块",
      "subsystemId": 1,
      "spacecraftId": 1,
      "singleId": 1
    }
  ]
}
```

### 4.3 删除模块

**功能描述**: 删除指定模块

**端点路径**: `DELETE /single/modules/{moduleId}`

**HTTP方法**: DELETE

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| moduleId | Long | 是 | 模块ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 4.4 编辑模块

**功能描述**: 编辑指定模块信息

**端点路径**: `PUT /single/modules/{moduleId}`

**HTTP方法**: PUT

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| moduleId | Long | 是 | 模块ID |

**请求体**:
```json
{
  "name": "新的模块名称",
  "subsystemId": 1,
  "spacecraftId": 1,
  "singleId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 5. 遥测代号绑定相关接口

### 5.1 绑定遥测代号

**功能描述**: 将遥测代号绑定到指定层级的对象（卫星/分系统/单机/模块）

**端点路径**: `POST /single/telemetry/bind`

**HTTP方法**: POST

**请求体**:
```json
{
  "level": "satellite",
  "id": 1,
  "telemetryCodeId": 101
}
```

**请求体字段说明**:
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | String | 是 | 绑定对象的层级：satellite/subsystem/single/module |
| id | Long | 是 | 对应层级的ID |
| telemetryCodeId | Long | 是 | 要绑定的遥测代号ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "level": "satellite",
    "id": 1,
    "telemetryCode": "TM001",
    "boundAt": "2024-01-15T10:30:00",
    "telemetryCodeId": 101
  }
}
```

**错误响应示例**:
```json
{
  "code": 409,
  "message": "父级未绑定，无法绑定子级",
  "data": null
}
```

### 5.2 查询卫星遥测代号

**功能描述**: 查询卫星绑定的遥测代号数据

**端点路径**: `GET /single/codesearch`

**HTTP方法**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| satelliteId | Long | 是 | 卫星ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 101,
      "serialNum": 1,
      "description": "温度遥测",
      "level": "satellite",
      "levelId": 1
    }
  ]
}
```

### 5.3 查询指定层级绑定的遥测代号

**功能描述**: 查询指定层级绑定的遥测代号

**端点路径**: `GET /single/telemetry/bound`

**HTTP方法**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | String | 是 | 层级：satellite/subsystem/single/module |
| id | Long | 是 | 对应层级的ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 101,
      "serialNum": 1,
      "description": "温度遥测",
      "level": "subsystem",
      "levelId": 1
    }
  ]
}
```

### 5.4 查询可绑定的遥测代号

**功能描述**: 查询可绑定的遥测代号（根据层级继承规则）

**端点路径**: `GET /single/telemetry/available`

**HTTP方法**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | String | 是 | 层级：satellite/subsystem/single/module |
| id | Long | 是 | 对应层级的ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 102,
      "serialNum": 2,
      "description": "压力遥测",
      "available": true
    }
  ]
}
```

### 5.5 删除遥测代号绑定

**功能描述**: 删除指定层级的遥测代号绑定

**端点路径**: `DELETE /single/telemetry/unbind`

**HTTP方法**: DELETE

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | String | 是 | 层级：satellite/subsystem/single/module |
| id | Long | 是 | 对应层级的ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 5.6 删除特定遥测代号绑定

**功能描述**: 删除特定的遥测代号绑定记录

**端点路径**: `DELETE /single/telemetry/unbind/{telemetryCodeId}`

**HTTP方法**: DELETE

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| telemetryCodeId | Long | 是 | 遥测代号ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 5.7 按序号解绑遥测代号

**功能描述**: 根据层级和遥测代号序号删除绑定

**端点路径**: `DELETE /single/telemetry/unbind-by-serial`

**HTTP方法**: DELETE

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | String | 是 | 层级：satellite/subsystem/single/module |
| levelId | Long | 是 | 对应层级的ID |
| serialNum | Integer | 是 | 遥测代号序号 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 404 | 资源不存在 |
| 409 | 冲突（如父级未绑定、已绑定等） |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都包含日志记录功能，会记录操作信息
2. 删除操作采用级联删除策略
3. 遥测代号绑定遵循层级继承规则
4. 分页查询默认每页10条记录
5. 所有必填参数需要进行验证，不能为空 