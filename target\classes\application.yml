server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: xtgl-satellite-system
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: root
  
  # MyBatis Plus 配置
  mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.xtgl.ssystem.common.entity

# 日志配置
logging:
  level:
    com.xtgl.ssystem: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 自定义配置
app:
  jwt:
    secret: xtgl-satellite-system-jwt-secret-key-for-authentication-2024
    expiration: 86400000 # 24小时（毫秒）