import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/main',
    name: 'MainPage',
    component: () => import('@/views/MainPage.vue'),
    meta: { title: '主页', requiresAuth: true }
  },
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/system',
    component: () => import('@/views/layout/Layout.vue'),
    redirect: '/system/spacecraft/list',
    meta: { requiresAuth: true },
    children: [
      // 航天器管理
      {
        path: '/system/spacecraft/list',
        name: 'SpacecraftList',
        component: () => import('@/views/spacecraft/SpacecraftList.vue'),
        meta: { title: '航天器列表', requiresAuth: true }
      },
      {
        path: '/system/spacecraft/telemetry/:id',
        name: 'SpacecraftTelemetry',
        component: () => import('@/views/spacecraft/SpacecraftTelemetry.vue'),
        meta: { title: '遥测代号查看', requiresAuth: true }
      },
      {
        path: '/system/spacecraft/create',
        name: 'SpacecraftCreate',
        component: () => import('@/views/spacecraft/SpacecraftCreate.vue'),
        meta: { title: '新建航天器', requiresAuth: true }
      },
      {
        path: '/system/spacecraft/upload/:id',
        name: 'SpacecraftUpload',
        component: () => import('@/views/spacecraft/SpacecraftUpload.vue'),
        meta: { title: '文件上传', requiresAuth: true }
      },
      {
        path: '/system/spacecraft/config',
        name: 'SpacecraftConfig',
        component: () => import('@/views/spacecraft/SpacecraftConfig.vue'),
        meta: { title: '单机配置', requiresAuth: true }
      },
      {
        path: '/system/spacecraft/config-main',
        name: 'SpacecraftConfigMain',
        component: () => import('@/views/spacecraft/ConfigMain.vue'),
        meta: { title: '配置遥测代号', requiresAuth: true }
      },

      {
        path: '/system/spacecraft/algorithm',
        name: 'AlgorithmManagement',
        component: () => import('@/views/spacecraft/AlgorithmManagement.vue'),
        meta: { title: '算法管理', requiresAuth: true }
      },
      // 权限管理
      {
        path: '/system/auth/user',
        name: 'UserManagement',
        component: () => import('@/views/auth/UserManagement.vue'),
        meta: { title: '用户管理', requiresAuth: true }
      },
      {
        path: '/system/auth/role',
        name: 'RoleManagement',
        component: () => import('@/views/auth/RoleManagement.vue'),
        meta: { title: '角色管理', requiresAuth: true }
      },
      // 系统管理
      {
        path: '/system/system/mqtt',
        name: 'MqttManagement',
        component: () => import('@/views/system/MqttManagement.vue'),
        meta: { title: 'MQTT管理', requiresAuth: true }
      },
      {
        path: '/system/system/log',
        name: 'OperationLog',
        component: () => import('@/views/system/OperationLog.vue'),
        meta: { title: '操作日志', requiresAuth: true }
      },
      // CORS测试页面
      {
        path: '/system/cors-test',
        name: 'CorsTest',
        component: () => import('@/views/CorsTest.vue'),
        meta: { title: 'CORS测试', requiresAuth: true }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  // 如果访问登录页面且已有token，跳转到主页
  if (to.path === '/login' && token) {
    next('/main')
    return
  }
  
  // 如果需要认证的页面但没有token，跳转到登录页
  if (to.meta.requiresAuth && !token) {
    next('/login')
    return
  }
  
  // 默认情况下允许访问
  next()
})

export default router 