package com.xtgl.ssystem.util.listener;

import com.xtgl.ssystem.common.event.SysLogEvent;
import com.xtgl.ssystem.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 系统日志事件监听器
 */
@Slf4j
@Component
public class SysLogEventListener {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 异步处理系统日志事件
     */
    @Async
    @EventListener
    public void handleSysLogEvent(SysLogEvent event) {
        try {
            operationLogService.saveOperationLog(event.getLogDto());
        } catch (Exception e) {
            log.error("处理系统日志事件失败", e);
        }
    }
} 