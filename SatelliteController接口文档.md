# 航天器管理接口文档

## 接口概述
航天器管理模块提供了航天器信息的增删改查、批次管理、MQTT连接测试、遥测代号数据管理等功能。

## 基础路径
```
/satellite
```

---

## 1. 航天器基础管理

### 1.1 新建航天器
- **功能描述**: 创建新的航天器信息
- **端点路径**: `/satellite/new`
- **HTTP方法**: `POST`
- **请求体**: 
```json
{
  "name": "航天器名称",
  "model": "航天器型号", 
  "header": "负责人",
  "company": "所属单位"
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "航天器名称",
    "model": "航天器型号",
    "header": "负责人", 
    "company": "所属单位",
    "createTime": "2024-01-01 10:00:00"
  }
}
```

### 1.2 批量删除航天器
- **功能描述**: 根据ID列表批量删除航天器，或删除所有航天器
- **端点路径**: `/satellite/delete`
- **HTTP方法**: `DELETE`
- **请求参数**:
  - `id` (可选): 航天器ID列表，多个ID用英文逗号分隔
  - `all` (可选): 是否删除所有，默认false
- **请求示例**: `/satellite/delete?id=1,2,3` 或 `/satellite/delete?all=true`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功", 
  "data": {
    "deleted": 3
  }
}
```

### 1.3 分页条件查询航天器
- **功能描述**: 根据条件分页查询航天器列表
- **端点路径**: `/satellite/search`
- **HTTP方法**: `GET`
- **请求参数**:
  - `model` (可选): 型号（模糊查询）
  - `name` (可选): 名称（模糊查询）
  - `header` (可选): 负责人（模糊查询）
  - `company` (可选): 所属单位（模糊查询）
  - `pageNo` (可选): 页码，默认1
  - `pageSize` (可选): 每页条数，默认10
- **请求示例**: `/satellite/search?name=天舟&pageNo=1&pageSize=10`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "天舟一号",
        "model": "TZ-1", 
        "header": "张三",
        "company": "航天科技集团",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 1.4 根据ID获取航天器
- **功能描述**: 根据航天器ID获取详细信息
- **端点路径**: `/satellite/{id}`
- **HTTP方法**: `GET`
- **路径参数**: 
  - `id`: 航天器ID
- **请求示例**: `/satellite/1`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "天舟一号",
    "model": "TZ-1",
    "header": "张三", 
    "company": "航天科技集团",
    "createTime": "2024-01-01 10:00:00"
  }
}
```

### 1.5 更新航天器
- **功能描述**: 更新航天器信息
- **端点路径**: `/satellite/update`
- **HTTP方法**: `PUT`
- **请求体**:
```json
{
  "id": 1,
  "name": "天舟一号(更新)",
  "model": "TZ-1-V2",
  "header": "李四",
  "company": "航天科技集团"
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "天舟一号(更新)",
    "model": "TZ-1-V2", 
    "header": "李四",
    "company": "航天科技集团",
    "updateTime": "2024-01-02 14:30:00"
  }
}
```

---

## 2. 批次管理

### 2.1 添加批次
- **功能描述**: 添加一个或多个批次号
- **端点路径**: `/satellite/batch`
- **HTTP方法**: `POST`
- **请求体**: 单个批次号（整数）或批次号数组
```json
2024
```
或
```json
[2024, 2025, 2026]
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "inserted": 3
  }
}
```

### 2.2 查询所有批次
- **功能描述**: 获取所有批次号列表
- **端点路径**: `/satellite/batch`
- **HTTP方法**: `GET`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [2024, 2025, 2026]
}
```

---

## 3. MQTT连接管理

### 3.1 MQTT连接测试
- **功能描述**: 测试指定MQTT连接的连通性
- **端点路径**: `/satellite/mqtttest`
- **HTTP方法**: `POST`
- **请求体**:
```json
{
  "mqtt_name": "MQTT连接名称"
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "连接成功",
    "connectionTime": "2024-01-01 15:30:00"
  }
}
```

### 3.2 获取所有MQTT连接名称
- **功能描述**: 获取系统中所有MQTT连接的名称列表
- **端点路径**: `/satellite/mqtttest`
- **HTTP方法**: `GET`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": ["MQTT_Connection_1", "MQTT_Connection_2", "MQTT_Connection_3"]
}
```

---

## 4. 遥测代号文件管理

### 4.1 上传遥测代号文件（仅检查格式）
- **功能描述**: 上传Excel文件并检查格式，不绑定到具体航天器
- **端点路径**: `/satellite/upload/file`
- **HTTP方法**: `POST`
- **请求参数**: 
  - `file`: Excel文件（multipart/form-data）
- **支持格式**: .xlsx, .xls
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "fileName": "telemetry_data.xlsx",
    "recordCount": 150,
    "validRecords": 148,
    "invalidRecords": 2,
    "message": "文件格式验证完成"
  }
}
```

### 4.2 批量上传遥测代号文件
- **功能描述**: 上传Excel文件，使用指定文件名作为批次标识
- **端点路径**: `/satellite/upload/batch`
- **HTTP方法**: `POST`
- **请求参数**:
  - `file`: Excel文件（multipart/form-data）
  - `fileName`: 文件名（批次标识）
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "fileName": "batch_2024_01",
    "recordCount": 200,
    "successCount": 195,
    "failureCount": 5,
    "message": "批量上传完成"
  }
}
```

### 4.3 导入遥测代号到航天器
- **功能描述**: 上传Excel文件并直接绑定到指定航天器
- **端点路径**: `/satellite/upload`
- **HTTP方法**: `POST`
- **请求参数**:
  - `file`: Excel文件（multipart/form-data）
  - `satelliteId`: 航天器ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "satelliteId": 1,
    "fileName": "telemetry_codes.xlsx",
    "importedCount": 180,
    "skippedCount": 5,
    "message": "导入完成"
  }
}
```

---

## 5. 遥测代号数据管理

### 5.1 查询遥测代号
- **功能描述**: 根据遥测代号ID或航天器ID查询遥测代号列表
- **端点路径**: `/satellite/telemetry`
- **HTTP方法**: `GET`
- **请求参数**:
  - `id` (可选): 遥测代号ID
  - `satelliteId` (可选): 航天器ID
- **请求示例**: `/satellite/telemetry?satelliteId=1`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "satelliteId": 1,
      "code": "TM001",
      "name": "姿态角度",
      "unit": "度",
      "description": "航天器姿态角度参数"
    },
    {
      "id": 2,
      "satelliteId": 1, 
      "code": "TM002",
      "name": "轨道高度",
      "unit": "千米",
      "description": "航天器轨道高度参数"
    }
  ]
}
```

### 5.2 删除遥测代号
- **功能描述**: 根据ID列表批量删除遥测代号
- **端点路径**: `/satellite/telemetry`
- **HTTP方法**: `DELETE`
- **请求参数**:
  - `id` (可选): 遥测代号ID列表，多个ID用英文逗号分隔
  - `all` (可选): 是否删除所有，默认false
- **请求示例**: `/satellite/telemetry?id=1,2,3`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "deleted": 3
  }
}
```

### 5.3 查询批次遥测代号
- **功能描述**: 根据文件名（批次标识）查询未绑定航天器的遥测代号数据
- **端点路径**: `/satellite/telemetry/batch`
- **HTTP方法**: `GET`
- **请求参数**:
  - `fileName`: 文件名（批次标识）
- **请求示例**: `/satellite/telemetry/batch?fileName=batch_2024_01`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 10,
      "satelliteId": null,
      "fileName": "batch_2024_01",
      "code": "TM100",
      "name": "温度传感器",
      "unit": "摄氏度",
      "description": "设备温度监测"
    }
  ]
}
```

### 5.4 删除批次遥测代号
- **功能描述**: 根据文件名（批次标识）删除临时遥测代号数据
- **端点路径**: `/satellite/telemetry/batch`
- **HTTP方法**: `DELETE`
- **请求参数**:
  - `fileName`: 文件名（批次标识）
- **请求示例**: `/satellite/telemetry/batch?fileName=batch_2024_01`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "fileName": "batch_2024_01",
    "deletedCount": 25,
    "message": "批次数据删除完成"
  }
}
```

### 5.5 绑定批次遥测代号到航天器
- **功能描述**: 将指定文件名（批次）的遥测代号数据绑定到航天器
- **端点路径**: `/satellite/telemetry/batch/bind`
- **HTTP方法**: `POST`
- **请求参数**:
  - `satelliteId`: 航天器ID
  - `fileName`: 文件名（批次标识）
- **请求示例**: `/satellite/telemetry/batch/bind?satelliteId=1&fileName=batch_2024_01`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "satelliteId": 1,
    "fileName": "batch_2024_01",
    "boundCount": 25,
    "message": "数据绑定完成"
  }
}
```

---

## 6. 航天器数据关联管理

### 6.1 查询航天器绑定文件信息
- **功能描述**: 获取指定航天器已绑定的文件信息
- **端点路径**: `/satellite/bound-file/{satelliteId}`
- **HTTP方法**: `GET`
- **路径参数**:
  - `satelliteId`: 航天器ID
- **请求示例**: `/satellite/bound-file/1`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "satelliteId": 1,
    "fileName": "satellite_1_telemetry.xlsx",
    "bindTime": "2024-01-01 16:20:00",
    "recordCount": 180,
    "lastUpdateTime": "2024-01-02 09:15:00"
  }
}
```

### 6.2 删除航天器绑定数据
- **功能描述**: 删除指定航天器的所有绑定遥测数据
- **端点路径**: `/satellite/bound-data/{satelliteId}`
- **HTTP方法**: `DELETE`
- **路径参数**:
  - `satelliteId`: 航天器ID
- **请求示例**: `/satellite/bound-data/1`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "satelliteId": 1,
    "deletedCount": 180,
    "message": "航天器绑定数据删除完成"
  }
}
```

---

## 错误响应格式

所有接口在出现错误时，都会返回统一的错误响应格式：

```json
{
  "code": 400,
  "message": "请求参数错误的具体描述",
  "data": null
}
```

### 常见错误码说明
- `400`: 请求参数错误
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

---

## 注意事项

1. 所有接口都支持跨域访问
2. 文件上传接口仅支持 .xlsx 和 .xls 格式的Excel文件
3. 批次号必须在 1-9999 范围内
4. 分页查询的页码从1开始
5. 删除操作支持单个删除和批量删除
6. 所有接口操作都会记录到系统日志中 