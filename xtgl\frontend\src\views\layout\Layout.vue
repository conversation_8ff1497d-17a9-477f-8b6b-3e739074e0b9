<template>
  <el-container class="app-container">
    <!-- 侧边栏 -->
    <el-aside width="240px">
      <div class="logo">
        <el-icon class="logo-icon"><monitor /></el-icon>
        <span>系统管理</span>
      </div>
      <el-scrollbar>
        <div class="menu-container">
          <!-- 航天器管理 -->
          <div class="menu-section">
            <div class="section-title">航天器管理</div>
            <el-menu
              router
              :default-active="activeMenu"
              background-color="#ffffff"
              text-color="#303133"
              active-text-color="#409EFF"
            >
              <el-menu-item index="/system/spacecraft/list">
                <el-icon><document /></el-icon>
                <span>航天器列表</span>
              </el-menu-item>
              <el-menu-item index="/system/spacecraft/config">
                <el-icon><setting /></el-icon>
                <span>单机配置</span>
              </el-menu-item>
              <el-menu-item index="/system/spacecraft/algorithm">
                <el-icon><cpu /></el-icon>
                <span>算法管理</span>
              </el-menu-item>
            </el-menu>
          </div>
          
          <!-- 权限管理 -->
          <div class="menu-section">
            <div class="section-title">权限管理</div>
            <el-menu
              router
              :default-active="activeMenu"
              background-color="#ffffff"
              text-color="#303133"
              active-text-color="#409EFF"
            >
              <el-menu-item index="/system/auth/user">
                <el-icon><user /></el-icon>
                <span>用户管理</span>
              </el-menu-item>
              <el-menu-item index="/system/auth/role">
                <el-icon><key /></el-icon>
                <span>角色管理</span>
              </el-menu-item>
            </el-menu>
          </div>
          
          <!-- 系统管理 -->
          <div class="menu-section">
            <div class="section-title">系统管理</div>
            <el-menu
              router
              :default-active="activeMenu"
              background-color="#ffffff"
              text-color="#303133"
              active-text-color="#409EFF"
            >
              <el-menu-item index="/system/system/mqtt">
                <el-icon><connection /></el-icon>
                <span>MQTT管理</span>
              </el-menu-item>
              <el-menu-item index="/system/system/log">
                <el-icon><document /></el-icon>
                <span>操作日志</span>
              </el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-scrollbar>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container>
      <!-- 头部 -->
      <el-header>
        <div class="header-left">
          <div class="back-button" @click="goToMain">
            <el-icon><house /></el-icon>
            <span>返回应用</span>
          </div>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleDropdownCommand">
            <span class="user-dropdown">
              <el-icon><user /></el-icon>
              {{ userInfo.account || '管理员' }}
              <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile" disabled>个人信息</el-dropdown-item>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 内容区域 -->
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userInfo = ref({})

const activeMenu = computed(() => {
  return route.path
})

// 获取用户信息
const getUserInfo = () => {
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  }
}

// 返回主页面
const goToMain = () => {
  router.push('/main')
}

// 下拉菜单处理
const handleDropdownCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消退出
    }
  } else if (command === 'profile') {
    ElMessage.info('个人信息功能暂未实现')
  }
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.app-container {
  height: 100%;
}

.el-aside {
  background-color: #ffffff;
  color: #303133;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e4e7ed;
}

.logo {
  height: 60px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #e4e7ed;
}

.logo-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 10px;
}

.menu-container {
  padding: 10px 0;
}

.menu-section {
  margin-bottom: 15px;
}

.section-title {
  padding: 5px 20px;
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

.el-menu {
  border-right: none;
}

.el-header {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409EFF;
}

.back-button .el-icon {
  margin-right: 5px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-dropdown .el-icon {
  margin-right: 5px;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style> 