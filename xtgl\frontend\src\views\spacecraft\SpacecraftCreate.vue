<template>
  <div class="create-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/system/spacecraft/list' }">
          <el-icon><HomeFilled /></el-icon>航天器列表
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ isEdit ? '编辑航天器' : '新建航天器' }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="spacecraft-form"
    >
      <!-- 航天器信息 -->
      <div class="section-title">
        <div class="divider"></div>
        <h3>航天器信息</h3>
      </div>
      
      <el-form-item label="型号" prop="model">
        <el-input v-model="form.model" placeholder="请输入航天器型号" />
      </el-form-item>
      
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入航天器名称" />
      </el-form-item>
      
      <el-form-item label="所属单位" prop="company">
        <el-input v-model="form.company" placeholder="请输入所属单位" />
      </el-form-item>
      
      <el-form-item label="负责人" prop="header">
        <el-input v-model="form.header" placeholder="请输入负责人" />
      </el-form-item>
      
      <el-form-item label="批次" prop="batch">
        <div class="batch-container">
          <el-select
            v-model="form.batch"
            placeholder="请选择批次"
            style="width: 300px"
          >
            <el-option
              v-for="item in batchOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button type="primary" @click="handleAddBatch">添加批次</el-button>
        </div>
      </el-form-item>
      
      <!-- MQTT信息 -->
      <div class="section-title">
        <div class="divider"></div>
        <h3>MQTT信息</h3>
      </div>
      
      <el-form-item label="MQTT名称" prop="mqttName">
        <div class="mqtt-container">
          <el-select
            v-model="form.mqttName"
            placeholder="请选择MQTT名称"
            style="width: 300px"
          >
            <el-option
              v-for="item in mqttOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
          <el-button type="primary" @click="handleTestMqtt" :loading="testingMqtt">测试连接</el-button>
        </div>
      </el-form-item>
      
      <!-- 按钮区域 -->
      <div class="button-container">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleNext">下一步</el-button>
        <el-button type="primary" @click="handleComplete">{{ isEdit ? '编辑完成' : '创建完成' }}</el-button>
      </div>
    </el-form>
    
    <!-- 批次输入对话框 -->
    <el-dialog
      v-model="batchDialog.visible"
      title="添加批次"
      width="30%"
    >
      <el-input v-model="batchDialog.value" placeholder="请输入批次号" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddBatch">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { HomeFilled } from '@element-plus/icons-vue'
import { createSatellite, updateSatellite, getSatelliteById, addBatch, getBatches, getMqttConnections, testMqttConnection } from '@/api/satellite'

const router = useRouter()
const route = useRoute()
const formRef = ref(null)
const isEdit = ref(!!route.query.id && route.query.id !== 'new')
const testingMqtt = ref(false)

// 表单数据
const form = reactive({
  id: (route.query.id && route.query.id !== 'new') ? route.query.id : null,
  model: '',
  name: '',
  company: '',
  header: '',
  batch: null,
  mqttName: '',
  description: ''
})

// 表单验证规则
const rules = {
  model: [{ required: true, message: '请输入航天器型号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入航天器名称', trigger: 'blur' }],
  company: [{ required: true, message: '请输入所属单位', trigger: 'blur' }],
  header: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  batch: [{ required: true, message: '请选择批次', trigger: 'change' }],
  mqttName: [{ required: true, message: '请选择MQTT名称', trigger: 'change' }]
}

// 批次选项
const batchOptions = ref([])
// MQTT选项
const mqttOptions = ref([])

// 批次输入对话框
const batchDialog = reactive({
  visible: false,
  value: ''
})

// 初始化数据
onMounted(async () => {
  await Promise.all([
    fetchBatches(),
    fetchMqttConnections()
  ])

  // 如果是编辑模式，获取航天器信息
  if (isEdit.value && route.query.id && route.query.id !== 'new') {
    await fetchSatelliteInfo()
  } else if (route.query.name) {
    // 如果是从上传页面返回，恢复表单数据
    form.name = route.query.name || ''
    form.model = route.query.model || ''
    form.company = route.query.company || ''
    form.header = route.query.header || ''
    form.batch = route.query.batch ? Number(route.query.batch) : null
    form.mqttName = route.query.mqttName || ''
  }
})

// 获取批次列表
const fetchBatches = async () => {
  try {
    const res = await getBatches()
    batchOptions.value = res.data.map(item => ({
      label: `批次 ${item}`,
      value: item
    }))
  } catch (error) {
    console.error('获取批次列表失败', error)
    ElMessage.error('获取批次列表失败')
  }
}

// 获取MQTT连接列表
const fetchMqttConnections = async () => {
  try {
    const res = await getMqttConnections()
    mqttOptions.value = res.data
  } catch (error) {
    console.error('获取MQTT连接列表失败', error)
    ElMessage.error('获取MQTT连接列表失败')
  }
}

// 获取航天器信息(编辑模式)
const fetchSatelliteInfo = async () => {
  try {
    const res = await getSatelliteById(form.id)
    // 将获取的航天器数据填充到表单中
    Object.assign(form, res.data)
  } catch (error) {
    console.error('获取航天器信息失败', error)
    ElMessage.error('获取航天器信息失败')
  }
}

// 处理添加批次
const handleAddBatch = () => {
  batchDialog.visible = true
  batchDialog.value = ''
}

// 确认添加批次
const confirmAddBatch = async () => {
  if (!batchDialog.value.trim()) {
    ElMessage.warning('请输入批次号')
    return
  }
  
  try {
    await addBatch(Number(batchDialog.value) || batchDialog.value)
    ElMessage.success('添加批次成功')
    
    // 更新批次列表
    await fetchBatches()
    
    // 选中新添加的批次
    form.batch = Number(batchDialog.value) || batchDialog.value
    
    // 关闭对话框
    batchDialog.visible = false
  } catch (error) {
    console.error('添加批次失败', error)
    ElMessage.error('添加批次失败')
  }
}

// 测试MQTT连接
const handleTestMqtt = async () => {
  if (!form.mqttName) {
    ElMessage.warning('请先选择MQTT名称')
    return
  }

  testingMqtt.value = true
  try {
    const res = await testMqttConnection({ mqtt_name: form.mqttName })
    if (res.data.connected) {
      ElMessage.success(`连接成功 (MQTT版本: ${res.data.mqttVersion}, 响应时间: ${res.data.timeCostMs}ms)`)
    } else {
      ElMessage.error(res.data.errorMsg || '连接失败')
    }
  } catch (error) {
    console.error('测试MQTT连接失败', error)
    ElMessage.error('测试MQTT连接失败')
  } finally {
    testingMqtt.value = false
  }
}

// 表单验证
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate((valid) => {
      if (valid) {
        resolve()
      } else {
        reject(new Error('请完成必填项'))
      }
    })
  })
}

// 处理下一步
const handleNext = async () => {
  try {
    await validateForm()
    
    // 不在此处创建航天器，只跳转到文件上传页面
    // 将表单数据通过路由传递
    router.push({
      path: `/system/spacecraft/upload/${form.id || 'new'}`,
      query: {
        name: form.name,
        model: form.model,
        company: form.company,
        header: form.header,
        batch: form.batch,
        mqttName: form.mqttName,
        isNew: isEdit.value ? 'false' : 'true' // 标记是否为新建
      }
    })
  } catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error(error.message || '请完善表单信息')
  }
}

// 处理创建完成
const handleComplete = async () => {
  try {
    await validateForm()
    
    // 如果是编辑模式，更新航天器信息
    if (isEdit.value) {
      await updateSatellite(form)
      ElMessage.success('航天器更新成功')
    } else {
      // 新建模式，创建航天器
      await createSatellite(form)
      ElMessage.success('航天器创建成功')
    }
    
    // 返回列表页
    router.push('/system/spacecraft/list')
  } catch (error) {
    console.error('保存航天器信息失败', error)
    ElMessage.error(error.message || '保存失败')
  }
}

// 取消
const handleCancel = () => {
  router.push('/system/spacecraft/list')
}
</script>

<style scoped>
.create-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
}

.breadcrumb {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.section-title .divider {
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  margin-right: 8px;
  border-radius: 2px;
}

.section-title h3 {
  font-size: 16px;
  margin: 0;
  font-weight: bold;
}

.spacecraft-form {
  max-width: 800px;
}

.batch-container,
.mqtt-container {
  display: flex;
  gap: 10px;
}

.button-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

:deep(.el-form-item__label)::before {
  content: '*';
  color: #ff4949;
  margin-right: 4px;
}
</style> 